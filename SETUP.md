# Analytics Platform Setup Guide

This guide covers the complete setup of the analytics platform with Pinot, Kafka, Superset, and Keycloak integration.

## Prerequisites

- Docker and Docker Compose installed
- At least 8GB RAM available for containers
- Ports 8080, 8088, 8098, 8099, 9000, 9092 available

## Architecture Overview

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Keycloak  │    │  Superset   │    │    Pinot    │
│   :8080     │◄──►│   :8088     │◄──►│   :9000     │
└─────────────┘    └─────────────┘    └─────────────┘
                           │
                   ┌─────────────┐
                   │ PostgreSQL  │
                   │   :5432     │
                   └─────────────┘
```

## Quick Start

1. **Clone and navigate to the project:**
   ```bash
   cd /path/to/analytics
   cd docker
   ```

2. **Start all services:**
   ```bash
   docker-compose up -d
   ```

3. **Access services:**
   - Superset: http://localhost:8088
   - Pinot Console: http://localhost:9000
   - Keycloak: http://localhost:8080 (if running locally)

## Common Issues and Solutions

### 1. Superset Pip Root User Warning

**Issue:** `WARNING: Running pip as the 'root' user can result in broken permissions`

**Solution:** Add `--root-user-action=ignore` to pip install command in docker-compose.yml:
```yaml
command: >
  bash -c "
    pip install --root-user-action=ignore psycopg2-binary authlib pinotdb flask_cors &&
    # ... rest of commands
  "
```

### 2. Superset Config File Mount Error

**Issue:** `IsADirectoryError: [Errno 21] Is a directory: '/app/superset_config.py'`

**Solution:** Ensure the config file path is correct in docker-compose.yml:
```yaml
volumes:
  - ../src/app/config/superset_config.py:/app/superset_config.py
  - ./volumes/superset:/app/superset_home
```

### 3. Keycloak OAuth Connection Issues

**Issue:** `HTTPConnectionPool(host='localhost', port=8080): Max retries exceeded`

**Solutions:**

#### For External Keycloak Instance:
Update environment variables in docker-compose.yml:
```yaml
environment:
  - ISSUER_URL=http://************:8080/realms/analytic
  - CLIENT_ID=superset-client
  - CLIENT_SECRET=your-actual-client-secret
  - ENV_DOMAIN=http://************:8080
```

#### For Containerized Keycloak:
```yaml
environment:
  - ISSUER_URL=http://keycloak:8080/realms/analytic
  - ENV_DOMAIN=http://keycloak:8080
```

### 4. Network Connectivity Issues

**Issue:** Containers cannot communicate with each other

**Solution:** Ensure all containers are on the same network:
```bash
# Connect external Keycloak to the network
docker network connect pinot-network keycloak
```

### 5. Keycloak Realm Configuration

**Issue:** `{"error":"HTTP 404 Not Found"}` when accessing realm

**Required Keycloak Setup:**

1. **Create Realm:**
   - Access Keycloak Admin Console
   - Create realm named `analytic`
   - Enable the realm

2. **Create Client:**
   - Client ID: `superset-client`
   - Client Type: `OpenID Connect`
   - Enable "Client authentication"
   - Valid redirect URIs: `http://localhost:8088/oauth-authorized/keycloak`

3. **Configure Client:**
   - Go to Credentials tab
   - Copy the Client Secret
   - Update docker-compose.yml with the secret

## Environment Variables Reference

### Superset Configuration
```yaml
environment:
  - SUPERSET_CONFIG_PATH=/app/superset_config.py
  - SUPERSET_SECRET_KEY=your-secret-key-change-in-production
  - FLASK_ENV=development
  - FLASK_DEBUG=1
  
  # Database
  - DB_USER=superset
  - DB_PASS=superset
  - DB_HOST=postgres
  - DB_PORT=5432
  - DB_NAME=superset
  
  # Redis
  - REDIS_HOST=redis
  - REDIS_PORT=6379
  - REDIS_DB=1
  - REDIS_CELERY_DB=0
  
  # Keycloak OAuth
  - SUPERSET_BASE_URL=http://localhost:8088
  - ISSUER_URL=http://************:8080/realms/analytic
  - CLIENT_ID=superset-client
  - CLIENT_SECRET=eWGMlszVqzMd5x1nlzDFdjnrSvzbcskX
  - ENV_DOMAIN=http://************:8080
```

## Troubleshooting Commands

### Check Container Status
```bash
docker-compose ps
docker logs superset --tail=20
docker logs keycloak --tail=20
```

### Test Network Connectivity
```bash
# Test if Superset can reach Keycloak
docker exec superset curl -s http://************:8080/realms/analytic

# Check environment variables
docker exec superset env | grep -E "(ISSUER_URL|CLIENT_ID)"
```

### Restart Services
```bash
# Restart specific service
docker-compose restart superset

# Restart all services
docker-compose down && docker-compose up -d
```

## Data Persistence

The setup includes persistent volumes for:
- `zookeeper-data` & `zookeeper-logs`: Zookeeper coordination data
- `kafka-data`: Kafka messages and topics
- `pinot-*-data`: Pinot segments and metadata
- `postgres`: Database data
- `redis`: Cache data
- `superset`: Superset configurations

## Security Notes

1. **Change default passwords** in production
2. **Use proper SSL certificates** for HTTPS
3. **Configure proper firewall rules**
4. **Use strong secrets** for JWT and OAuth
5. **Enable proper authentication** in Pinot and Kafka

## Performance Tuning

### Memory Allocation
```yaml
# Pinot services memory settings
environment:
  JAVA_OPTS: "-Xms4G -Xmx16G -XX:+UseG1GC"
```

### Volume Optimization
- Use SSD storage for better performance
- Monitor disk usage regularly
- Set up log rotation

## Monitoring

### Health Checks
- Superset: http://localhost:8088/health
- Pinot Controller: http://localhost:9000
- Check container logs regularly

### Metrics
- Enable JMX for Pinot components
- Use Superset's built-in monitoring
- Monitor container resource usage

## Advanced Configuration

### Custom Superset Configuration

The superset config file (`src/app/config/superset_config.py`) includes:
- OAuth integration with Keycloak
- Custom security manager
- Redis caching configuration
- Database connection settings
- Feature flags and permissions

### Keycloak Integration Details

**OAuth Flow:**
1. User clicks "Login with Keycloak" in Superset
2. Redirected to Keycloak authentication
3. After successful login, redirected back to Superset
4. Superset validates JWT token and creates/updates user

**Required Keycloak Client Settings:**
```
Client ID: superset-client
Client Protocol: openid-connect
Access Type: confidential
Valid Redirect URIs: http://localhost:8088/oauth-authorized/keycloak
```

### Database Schema

**PostgreSQL databases created:**
- `superset`: Superset metadata (dashboards, users, etc.)

### Network Configuration

**Docker Network:** `pinot-network`
- Type: Bridge
- All containers communicate using container names
- External Keycloak connects via IP address

## Specific Error Solutions

### "The request to sign in was denied"

**Possible causes:**
1. **Incorrect client secret** - Verify in Keycloak client credentials
2. **Wrong redirect URI** - Must match exactly in Keycloak client
3. **Realm not accessible** - Check if realm exists and is enabled
4. **Network connectivity** - Ensure Superset can reach Keycloak

**Debug steps:**
```bash
# Test Keycloak connectivity
docker exec superset curl -v http://************:8080/realms/analytic

# Check Superset environment
docker exec superset env | grep -E "(ISSUER_URL|CLIENT_ID|CLIENT_SECRET)"

# Monitor Superset logs during login attempt
docker logs superset -f
```

### Volume Permission Issues

**Issue:** Permission denied when mounting volumes

**Solution:**
```bash
# Fix volume permissions
sudo chown -R 1000:1000 docker/volumes/
sudo chmod -R 755 docker/volumes/
```

### Port Conflicts

**Issue:** Port already in use

**Solution:**
```bash
# Check what's using the port
sudo netstat -tulpn | grep :8088

# Kill the process or change port in docker-compose.yml
```

## Production Deployment

### Security Checklist
- [ ] Change all default passwords
- [ ] Use HTTPS with proper certificates
- [ ] Configure firewall rules
- [ ] Enable audit logging
- [ ] Set up backup procedures
- [ ] Configure monitoring and alerting

### Performance Optimization
- [ ] Tune JVM settings for Pinot
- [ ] Configure Redis persistence
- [ ] Set up database connection pooling
- [ ] Enable compression for data transfer
- [ ] Configure log rotation

### Backup Strategy
```bash
# Backup volumes
docker run --rm -v docker_postgres:/data -v $(pwd):/backup alpine tar czf /backup/postgres-backup.tar.gz /data

# Backup configurations
tar czf config-backup.tar.gz src/app/config/ docker/docker-compose.yml
```

## Support

For issues not covered in this guide:
1. Check container logs first: `docker logs <container-name>`
2. Verify network connectivity between containers
3. Confirm configuration files are properly mounted
4. Test individual components separately
5. Check Keycloak admin console for client configuration
6. Verify environment variables are set correctly
