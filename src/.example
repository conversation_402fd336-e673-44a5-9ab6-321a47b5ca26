

# Keycloak Auth
OAUTH_USERNAME=<EMAIL>
OAUTH_PASSWORD=Test
SQLALCHEMY_DATABASE_URI="********************************************/superset"
REDIS_HOST=""
REDIS_PORT=""
SUPERSET_SECRET_KEY=""
SUPERSET_BASE_URL=http://localhost:8088
KEYCLOAK_ISSUER_URL = http://localhost:8080/realms/superset
KEYCLOAK_CLIENT_ID = superset-client
KEYCLOAK_CLIENT_SECRET = 2UyzyO5eo4fXbOaXEINxdYFlTIKfDMyQ
JWT_PUBLIC_KEY="JWT_PUBLIC_KEY"

# SPOG DOMAIN
ENV_DOMAIN=https://dev.spogconnected.com
