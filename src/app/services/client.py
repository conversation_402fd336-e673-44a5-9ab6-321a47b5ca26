import logging
import urllib.parse
from typing import List

import requests


class SupersetClient:
    def __init__(self, base_url, token):
        self.base_url = base_url
        self.session = requests.Session()
        self.headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json",
            "Accept": "application/json",
        }

    def create_role(self, role_name):
        resp = self.session.post(
            f"{self.base_url}/api/v1/security/roles",
            headers=self.headers,
            json={"name": role_name},
        )
        logging.info(f"create_role:{resp}")
        if resp.status_code == 422:
            logging.info(f"Role '{role_name}' already exists")
        elif resp.ok:
            logging.info(f"Role '{role_name}' created")
        else:
            logging.error(f"Role creation failed: {resp.text}")

    def get_role_id_by_name(self, role_name):
        rn = role_name
        query = urllib.parse.quote_plus(
            f'{{"filters":[{{"col":"name","opr":"eq","value":"{rn}"}}]}}'
        )
        url = f"{self.base_url}/api/v1/security/roles/?q={query}"
        logging.info(f"url:{url}")
        resp = self.session.get(
            url,
            headers=self.headers,
        )
        logging.info(f"get_role_id_by_name:{resp.json()}")
        resp.raise_for_status()
        result = resp.json()["result"]
        if not result:
            raise ValueError("Role not found")
        return result[0]["id"]

    def get_table_ids_by_names(self, table_names: List[str]) -> List[int]:
        url = (
            f'{self.base_url}/api/v1/dataset/?q={{"page":0,"page_size":1000}}'
        )
        resp = self.session.get(url, headers=self.headers)
        logging.info(f"get_table_ids_by_names:{resp.json()}")
        resp.raise_for_status()
        datasets = resp.json().get("result", [])
        return [
            d["id"] for d in datasets if d.get("table_name") in table_names
        ]

    def add_rls_policy(
        self,
        role_id,
        filter_type,
        filter_clause,
        tables,
        description,
        group_key,
    ):
        payload = {
            "clause": filter_clause,
            "description": description,
            "filter_type": filter_type,
            "group_key": group_key,
            "roles": [role_id],
            "tables": tables,  # send all table IDs at once
            "name": f"rls_multi_{role_id}",
        }
        resp = self.session.post(
            f"{self.base_url}/api/v1/rowlevelsecurity/",
            headers=self.headers,
            json=payload,
        )
        logging.info(f"add_rls_policy{resp}")
        if resp.status_code == 422:
            logging.info(f"RLS already exists for role ID {role_id}")
        elif resp.ok:
            logging.info(
                f"RLS applied for role ID {role_id} on tables {tables}"
            )
        else:
            logging.error(f"RLS failed: {resp.status_code} - {resp.text}")
