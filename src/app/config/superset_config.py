import logging
import os

import jwt
import requests
from flask import g, session
from flask_appbuilder.security.manager import AUTH_OAUTH
from flask_appbuilder.security.sqla.models import PermissionView
from flask_caching.backends.rediscache import RedisCache
from superset.security import SupersetSecurityManager


def env(key, default=None):
    """
    Helper function to get environment variables with a default value.
    """
    return os.getenv(key, default)


# --- Redis Configuration ---
# Base URL for Redis connection, constructed from environment variables.
REDIS_BASE_URL = (
    f"{env('REDIS_PROTO', 'redis')}://"
    f"{env('REDIS_HOST', 'localhost')}:{env('REDIS_PORT', 6379)}"
)


# Additional URL parameters for Redis (if any).
REDIS_URL_PARAMS = ""

# Build full Redis URLs for different purposes (Cache and Celery).
# Uses database 1 for cache by default.
CACHE_REDIS_URL = f"{REDIS_BASE_URL}/{env('REDIS_DB', 1)}{REDIS_URL_PARAMS}"
# Uses database 0 for Celery by default.
CELERY_REDIS_URL = (
    f"{REDIS_BASE_URL}/{env('REDIS_CELERY_DB', 0)}{REDIS_URL_PARAMS}"
)


# --- Mapbox Configuration ---
MAPBOX_API_KEY = env("MAPBOX_API_KEY", "")


# --- Cache Configuration ---
# Configuration for Flask-Caching.
CACHE_CONFIG = {
    "CACHE_TYPE": "RedisCache",
    "CACHE_DEFAULT_TIMEOUT": 300,  # 5 minutes
    "CACHE_KEY_PREFIX": "superset_",
    "CACHE_REDIS_URL": CACHE_REDIS_URL,
}
# Separate configuration for data caching, can be overridden if needed.
DATA_CACHE_CONFIG = CACHE_CONFIG


# --- Database Configuration ---
# URI for the main Superset database.
DB_USER = env("DB_USER")
DB_PASS = env("DB_PASS")
DB_HOST = env("DB_HOST")
DB_PORT = env("DB_PORT")
DB_NAME = env("DB_NAME")
SQLALCHEMY_DATABASE_URI = (
    f"postgresql+psycopg2://{DB_USER}:{DB_PASS}@"
    f"{DB_HOST}:{DB_PORT}/{DB_NAME}"
)
SQLALCHEMY_TRACK_MODIFICATIONS = True


# --- Celery Configuration ---
# Defines configuration for Celery workers.
class CeleryConfig:
    imports = ("superset.sql_lab",)
    broker_url = CELERY_REDIS_URL
    result_backend = CELERY_REDIS_URL


CELERY_CONFIG = CeleryConfig


# --- Results Backend Configuration ---
# Defines a results backend using Redis.
RESULTS_BACKEND = RedisCache(
    host=env("REDIS_HOST", "localhost"),
    port=env("REDIS_PORT", 6379),
    key_prefix="superset_results",
)


# --- Security Overrides ---
# It is critical to set a custom, complex
# secret key in a production environment.
# The key below is for example purposes only.
# Use `openssl rand -base64 42` to generate a secure key.
SECRET_KEY = env(
    "SECRET_KEY", ("ZJ4vmEZE4nC9AAop9BZYp/c/kcR//b1JN+wlPzbzh5Dmo2OCdw97Rrgd")
)


CUSTOM_ROLE = "DashboardOwner"

AUTH_TYPE = AUTH_OAUTH
AUTH_USER_REGISTRATION = True
AUTH_USER_REGISTRATION_ROLE = "Public"
AUTH_ROLE_SYNC_AT_LOGIN = False

JWT_ALGORITHMS = ["RS256"]
JWT_ALGORITHM = "RS256"

JWT_PUBLIC_KEY = os.getenv("JWT_PUBLIC_KEY")

FEATURE_FLAGS = {
    "DASHBOARD_NATIVE_FILTERS": True,
    "DASHBOARD_CROSS_FILTERS": True,
    "DASHBOARD_RBAC": True,
    "ENABLE_TEMPLATE_PROCESSING": True,
    "GENERIC_CHART_AXES": True,
    "LISTVIEWS_DEFAULT_CARD_VIEW": True,
    "API_SWAGGER_ENABLED": True,
    "ENABLE_SWAGGER_UI": True,
    "OPENAPI_AUTO_SCHEMA": True,
    "ENABLE_CHART_OWNER_FILTER": True,
    "ENABLE_DASHBOARD_OWNER_FILTER": True,
    "ENABLE_BROAD_ACTIVITY_ACCESS": False,
    "EMBEDDED_SUPERSET": True,
    "ENABLE_JWT_LOGIN": True,
    "ALLOW_JWT_ALGORITHMS": ["RS256"],
}
ENABLE_PROXY_FIX = True
FAB_API_SWAGGER_UI = True
FAB_ADD_SECURITY_API = True
FAB_ADD_SECURITY_PERMISSION_VIEW = True
FAB_ADD_SECURITY_VIEW_MENU_VIEW = True
FAB_ADD_SECURITY_PERMISSION_VIEWS_VIEW = True
WTF_CSRF_ENABLED = False


SUPERSET_BASE_URL = os.getenv("SUPERSET_BASE_URL")
ISSUER_URL = os.getenv("ISSUER_URL")
CLIENT_ID = os.getenv("CLIENT_ID")
CLIENT_SECRET = os.getenv("CLIENT_SECRET")
SPOG_BASE_URL = os.getenv("ENV_DOMAIN")

LOGOUT_REDIRECT_URL = (
    f"{ISSUER_URL}/protocol/openid-connect/logout?"
    f"client_id={CLIENT_ID}&"
    f"post_logout_redirect_uri={SUPERSET_BASE_URL}/login/"
)

OAUTH_PROVIDERS = [
    {
        "name": "keycloak",
        "token_key": "access_token",
        "icon": "fa-address-card",
        "token_alg": "RS256",
        "remote_app": {
            "client_id": CLIENT_ID,
            "client_secret": CLIENT_SECRET,
            "access_token_url": (
                f"{ISSUER_URL}/protocol/openid-connect/token"
            ),
            "authorize_url": (f"{ISSUER_URL}/protocol/openid-connect/auth"),
            "jwks_uri": f"{ISSUER_URL}/protocol/openid-connect/certs",
            "client_kwargs": {
                "scope": "openid profile email",
                "token_endpoint_auth_method": "client_secret_post",
            },
        },
    }
]


# -------------------------------
# CUSTOM SECURITY MANAGER
# -------------------------------


class CustomSecurityManager(SupersetSecurityManager):
    def get_account_id(self, organization_id: int, token: str):
        url = f"{SPOG_BASE_URL}/v1/glass/accounts/{organization_id}/info"
        headers = {
            "accept": "application/json",
            "Authorization": f"Bearer {token}",
        }
        try:
            logging.info(f"get_account_id url:{url}")
            response = requests.get(url, headers=headers, timeout=30)
            response.raise_for_status()
            data = response.json()
            logging.info(f"account_id :{data.get('id')}")
            return data.get("id")
        except Exception as e:
            logging.error(f"Failed to fetch account_id: {e}")
            return None

    def oauth_user_info(self, provider, response=None):
        if provider != "keycloak" or not response:
            return {}
        access_token = response.get("access_token")
        if not access_token:
            logging.error("access token not found")
            return {}
        try:
            decoded = jwt.decode(
                access_token, options={"verify_signature": False}
            )
            organization = decoded.get("organization")
            logging.info(f"organization_info:{organization}")
            logging.info(f"organization_id:{organization.get('id')}")
            account_id = self.get_account_id(
                organization.get("id"), access_token
            )
            logging.info(f"account_id:{account_id}")
            session["account_id"] = account_id
            return {
                "username": decoded.get("preferred_username"),
                "email": decoded.get("email"),
                "first_name": decoded.get("given_name", ""),
                "last_name": decoded.get("family_name", ""),
                "account_id": account_id,
                "roles": decoded.get("realm_access", {}).get("roles", []),
            }
        except Exception as e:
            logging.error(f"ERROR Failed to decode JWT: {e}")
            return {}

    def ensure_dashboard_owner_permissions(self):
        role = self.find_role(CUSTOM_ROLE) or self.add_role(CUSTOM_ROLE)

        # Full list of (view, permission) tuples
        full_permissions = [
            ("SavedQuery", "can_read"),
            ("SavedQuery", "can_write"),
            ("CssTemplate", "can_read"),
            ("CssTemplate", "can_write"),
            ("ReportSchedule", "can_read"),
            ("ReportSchedule", "can_write"),
            ("Chart", "can_read"),
            ("Chart", "can_write"),
            ("Annotation", "can_read"),
            ("Annotation", "can_write"),
            ("Dataset", "can_read"),
            ("Dataset", "can_write"),
            ("Dashboard", "can_read"),
            ("Dashboard", "can_write"),
            ("Database", "can_read"),
            ("Database", "can_upload"),
            ("Database", "can_excel_upload"),
            ("ResetMyPasswordView", "can_this_form_get"),
            ("ResetMyPasswordView", "can_this_form_post"),
            ("UserInfoApi", "can_userinfo"),
            ("OpenApi", "can_get"),
            ("SwaggerView", "can_show"),
            ("MenuApi", "can_get"),
            ("AsyncEventsRestApi", "can_list"),
            ("AdvancedDataType", "can_read"),
            ("AvailableDomains", "can_read"),
            ("CacheRestApi", "can_Invalidate"),
            ("Chart", "can_export"),
            ("DashboardFilterStateRestApi", "can_write"),
            ("DashboardFilterStateRestApi", "can_read"),
            ("DashboardPermalinkRestApi", "can_write"),
            ("DashboardPermalinkRestApi", "can_read"),
            ("Dashboard", "can_export"),
            ("Dashboard", "can_get_embedded"),
            ("Dashboard", "can_delete_embedded"),
            ("Dashboard", "can_cache_dashboard_screenshot"),
            ("Dataset", "can_export"),
            ("Dataset", "can_get_or_create_dataset"),
            ("Dataset", "can_duplicate"),
            ("Datasource", "can_get_column_values"),
            ("EmbeddedDashboard", "can_read"),
            ("Explore", "can_read"),
            ("ExploreFormDataRestApi", "can_write"),
            ("ExploreFormDataRestApi", "can_read"),
            ("ExplorePermalinkRestApi", "can_write"),
            ("ExplorePermalinkRestApi", "can_read"),
            ("ImportExportRestApi", "can_import"),
            ("ImportExportRestApi", "can_export"),
            ("Tag", "can_write"),
            ("Tag", "can_bulk_create"),
            ("Tag", "can_read"),
            ("SQLLab", "can_get_results"),
            ("SQLLab", "can_execute_query"),
            ("SQLLab", "can_estimate_query_cost"),
            ("SQLLab", "can_read"),
            ("SQLLab", "can_format_sql"),
            ("DynamicPlugin", "can_show"),
            ("DynamicPlugin", "can_list"),
            ("Api", "can_query"),
            ("Api", "can_query_form_data"),
            ("Datasource", "can_get"),
            ("Datasource", "can_save"),
            ("Datasource", "can_external_metadata"),
            ("Datasource", "can_samples"),
            ("KV", "can_store"),
            ("KV", "can_value"),
            ("SavedQuery", "can_add"),
            ("SavedQuery", "can_delete"),
            ("SavedQuery", "can_list"),
            ("SavedQuery", "can_show"),
            ("Superset", "can_sqlab_history"),
            ("Superset", "can_fetch_datasource_metadata"),
            ("Superset", "can_dashboard_permalink"),
            ("Superset", "can_explore_json"),
            ("Superset", "can_log"),
            ("Superset", "can_slice"),
            ("Superset", "can_dashboard"),
            ("Superset", "can_explore"),
            ("TableSchemaView", "can_expanded"),
            ("TableSchemaView", "can_post"),
            ("TableSchemaView", "can_delete"),
            ("TabStateView", "can_post"),
            ("TabStateView", "can_delete_query"),
            ("Tags", "can_list"),
            ("TagView", "can_tag"),
            ("Log", "can_recent_activity"),
            ("SecurityRestApi", "can_read"),
            ("RowLevelSecurity", "can_read"),
            ("Home", "can_access"),
            ("Superset", "can_share_dashboard"),
            ("Superset", "can_share_chart"),
            ("Superset", "can_sqlab"),
            ("Dashboard", "can_view_query"),
            ("Dashboard", "can_view_chart_via_table"),
            ("Dashboard", "can_drill"),
            ("Chart", "can_tag"),
            ("Dashboard", "can_tag"),
            ("PermissionModelView", "can_list"),
            ("ViewMenuModelView", "can_list"),
            ("PermissionViewModelView", "can_list"),
            ("all_database_access", "all_database_access"),
        ]

        menu_access = [
            "Data",
            "Datasets",
            "Dashboards",
            "Charts",
            "Databases",
            "Manage",
            "Plugins",
            "Css Templates",
            "Tags",
            "Alerts & Reports",
            "Annotation Layers",
            "SQL Lab",
            "SQL Editor",
            "Saved Queries",
            "Base Permissions",
            "Views/Menus",
            "Permission on Views/Menus",
        ]

        for view_name, perm_name in full_permissions:
            pv = (
                self.get_session.query(PermissionView)
                .join(PermissionView.permission)
                .join(PermissionView.view_menu)
                .filter(PermissionView.view_menu.has(name=view_name))
                .filter(PermissionView.permission.has(name=perm_name))
                .first()
            )
            if pv and pv not in role.permissions:
                self.add_permission_role(role, pv)

        for menu in menu_access:
            pv = self.add_permission_view_menu("menu_access", menu)
            if pv and pv not in role.permissions:
                self.add_permission_role(role, pv)

        self.get_session.commit()

    def auth_user_oauth(self, userinfo):
        if not userinfo:
            return None

        username = userinfo.get("username")
        email = userinfo.get("email")
        first_name = userinfo.get("first_name")
        last_name = userinfo.get("last_name")
        roles_from_token = userinfo.get("roles", [])

        if not username:
            return None

        user = self.find_user(username=username)
        if not user:
            user = self.add_user(
                username=username,
                first_name=first_name,
                last_name=last_name,
                email=email,
                role=self.find_role("Public"),
            )

        ignored = {
            "offline_access",
            "default-roles-superset",
            "uma_authorization",
        }
        effective_roles = [r for r in roles_from_token if r not in ignored]

        if "DistributorAdmin" in effective_roles:
            admin_role = self.find_role("Admin") or self.add_role("Admin")
            user.roles = [admin_role]
        else:
            self.ensure_dashboard_owner_permissions()
            dashboard_role = self.find_role(CUSTOM_ROLE)
            user.roles = [dashboard_role]

        self.get_session.commit()
        return user

    def load_user_jwt(self, _jwt_header, jwt_data):
        username = jwt_data.get("preferred_username")
        user = self.find_user(username=username)
        if user and user.is_active:
            g.user = user
            return user
        return None


CUSTOM_SECURITY_MANAGER = CustomSecurityManager


# -------------------------------
# JINJA CONTEXT FOR RLS
# -------------------------------
JINJA_CONTEXT_ADDONS = {
    "account_id": lambda: session.get("account_id"),
}
