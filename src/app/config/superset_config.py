import logging
import os

import jwt
import requests
from flask import Flask, current_app, g, redirect, session
from flask_appbuilder.security.manager import AUTH_OAUTH
from flask_caching.backends.rediscache import RedisCache
from flask_login import current_user, logout_user
from superset.security import SupersetSecurityManager


def env(key, default=None):
    """
    Helper function to get environment variables with a default value.
    """
    return os.getenv(key, default)


# --- Redis Configuration ---
# Base URL for Redis connection, constructed from environment variables.
REDIS_BASE_URL = (
    f"{env('REDIS_PROTO', 'redis')}://"
    f"{env('REDIS_HOST', 'localhost')}:{env('REDIS_PORT', 6379)}"
)


# Additional URL parameters for Redis (if any).
REDIS_URL_PARAMS = ""

# Build full Redis URLs for different purposes (Cache and Celery).
# Uses database 1 for cache by default.
CACHE_REDIS_URL = f"{REDIS_BASE_URL}/{env('REDIS_DB', 1)}{REDIS_URL_PARAMS}"
# Uses database 0 for Celery by default.
CELERY_REDIS_URL = (
    f"{REDIS_BASE_URL}/{env('REDIS_CELERY_DB', 0)}{REDIS_URL_PARAMS}"
)


# --- Mapbox Configuration ---
MAPBOX_API_KEY = env("MAPBOX_API_KEY", "")


# --- Cache Configuration ---
# Configuration for Flask-Caching.
CACHE_CONFIG = {
    "CACHE_TYPE": "RedisCache",
    "CACHE_DEFAULT_TIMEOUT": 300,  # 5 minutes
    "CACHE_KEY_PREFIX": "superset_",
    "CACHE_REDIS_URL": CACHE_REDIS_URL,
}
# Separate configuration for data caching, can be overridden if needed.
DATA_CACHE_CONFIG = CACHE_CONFIG


# --- Database Configuration ---
# URI for the main Superset database.
DB_USER = env("DB_USER")
DB_PASS = env("DB_PASS")
DB_HOST = env("DB_HOST")
DB_PORT = env("DB_PORT")
DB_NAME = env("DB_NAME")
SQLALCHEMY_DATABASE_URI = (
    f"postgresql+psycopg2://{DB_USER}:{DB_PASS}@"
    f"{DB_HOST}:{DB_PORT}/{DB_NAME}"
)
SQLALCHEMY_TRACK_MODIFICATIONS = True


# --- Celery Configuration ---
# Defines configuration for Celery workers.
class CeleryConfig:
    imports = ("superset.sql_lab",)
    broker_url = CELERY_REDIS_URL
    result_backend = CELERY_REDIS_URL


CELERY_CONFIG = CeleryConfig


# --- Results Backend Configuration ---
# Defines a results backend using Redis.
RESULTS_BACKEND = RedisCache(
    host=env("REDIS_HOST", "localhost"),
    port=env("REDIS_PORT", 6379),
    key_prefix="superset_results",
)


# --- Security Overrides ---
# It is critical to set a custom, complex
# secret key in a production environment.
# The key below is for example purposes only.
# Use `openssl rand -base64 42` to generate a secure key.
SECRET_KEY = env(
    "SECRET_KEY", ("your-secret-key-change-in-production")
)


CUSTOM_ROLE = "DashboardOwner"
TIMEOUT = 10

AUTH_TYPE = AUTH_OAUTH
AUTH_USER_REGISTRATION = True
AUTH_USER_REGISTRATION_ROLE = "Public"
AUTH_ROLE_SYNC_AT_LOGIN = False

JWT_ALGORITHMS = ["RS256"]
JWT_ALGORITHM = "RS256"

# JWT_PUBLIC_KEY = os.getenv("JWT_PUBLIC_KEY")

JWT_PUBLIC_KEY='''
-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAxOPEaKRLQbljs4APEcXjb3PhnDKNMJ+h03
wyaX28nN+iIjXG4K9qViQNwgBD+ChYkvv9bl4/pyfYQSqassIYV9tPE0QilDvY/HJEyFIpu/nSDuUj
ZYVGagNM9Ho9dvYlwe+LrgXw/3jr40iPO55a93Qrvv/JDMHfROsWRzhXuCiW4bkMHB9rfwMEg6VVAWimXT
VEbDqaM2oPE17fEIguJo2KMQvnjSRRiWJ1lqdQeBO6wzXT8RdVeljTVeWOm7ajpslvUvnbUW5D80Tq1Wn/y
qvmiELwvFSxXx9fJogt5Ju185jsED7ek1Ku5IVy3kiRLQn6ZIrwPqoFu449ZLpb3QIDAQAB
-----END PUBLIC KEY-----'''

FEATURE_FLAGS = {
    "DASHBOARD_NATIVE_FILTERS": True,
    "DASHBOARD_CROSS_FILTERS": True,
    "DASHBOARD_RBAC": True,
    "ENABLE_TEMPLATE_PROCESSING": True,
    "GENERIC_CHART_AXES": True,
    "LISTVIEWS_DEFAULT_CARD_VIEW": True,
    "API_SWAGGER_ENABLED": True,
    "ENABLE_SWAGGER_UI": True,
    "OPENAPI_AUTO_SCHEMA": True,
    "ENABLE_CHART_OWNER_FILTER": True,
    "ENABLE_DASHBOARD_OWNER_FILTER": True,
    "ENABLE_BROAD_ACTIVITY_ACCESS": False,
    "EMBEDDED_SUPERSET": True,
    "ENABLE_JWT_LOGIN": True,
    "ALLOW_JWT_ALGORITHMS": ["RS256"],
}
ENABLE_PROXY_FIX = True
FAB_API_SWAGGER_UI = True
FAB_ADD_SECURITY_API = True
FAB_ADD_SECURITY_PERMISSION_VIEW = True
FAB_ADD_SECURITY_VIEW_MENU_VIEW = True
FAB_ADD_SECURITY_PERMISSION_VIEWS_VIEW = True
WTF_CSRF_ENABLED = False


# IMPORTANT: Enabling SESSION_COOKIE_DOMAIN causes logout issues.
# When set to ".spogconnected.com", the logout view does
# not properly clear the session cookie,
# SESSION_COOKIE_DOMAIN = ".spogconnected.com"


SUPERSET_BASE_URL = os.getenv("SUPERSET_BASE_URL")
ISSUER_URL = os.getenv("ISSUER_URL")
CLIENT_ID = os.getenv("CLIENT_ID")
CLIENT_SECRET = os.getenv("CLIENT_SECRET")
SPOG_BASE_URL = os.getenv("ENV_DOMAIN")


LOGOUT_REDIRECT_URL = (
    f"{ISSUER_URL}/protocol/openid-connect/logout?"
    f"client_id={CLIENT_ID}&"
    f"post_logout_redirect_uri={SUPERSET_BASE_URL}/login/"
)

OAUTH_PROVIDERS = [
    {
        "name": "keycloak",
        "token_key": "access_token",
        "icon": "fa-address-card",
        "token_alg": "RS256",
        "remote_app": {
            "client_id": CLIENT_ID,
            "client_secret": CLIENT_SECRET,
            "access_token_url": (
                f"{ISSUER_URL}/protocol/openid-connect/token"
            ),
            "authorize_url": (f"{ISSUER_URL}/protocol/openid-connect/auth"),
            "jwks_uri": f"{ISSUER_URL}/protocol/openid-connect/certs",
            "client_kwargs": {
                "scope": "openid profile email",
                "token_endpoint_auth_method": "client_secret_post",
            },
        },
    }
]

# UI Configuration
ANALYTICS_APP_ROUTE = f"{os.getenv('ANALYTICS_APP_ROUTE')}/analytics"
TALISMAN_ENABLED = False
OVERRIDE_HTTP_HEADERS = {
    "X-Frame-Options": "ALLOWALL",
    "Content-Security-Policy": f"frame-ancestors 'self' {ANALYTICS_APP_ROUTE}",
}

SESSION_COOKIE_SAMESITE = "None"
SESSION_COOKIE_SECURE = True

WTF_CSRF_COOKIE_SAMESITE = "None"
WTF_CSRF_COOKIE_SECURE = True

# -------------------------------
# CUSTOM SECURITY MANAGER
# -------------------------------


class CustomSecurityManager(SupersetSecurityManager):
    # def is_token_active(self, token: str) -> bool:
    #     try:
    #         introspect_url = (
    #             f"{ISSUER_URL}/protocol/openid-connect/token/introspect"
    #         )
    #         data = {
    #             "token": token,
    #             "client_id": CLIENT_ID,
    #             "client_secret": CLIENT_SECRET,
    #         }
    #         headers = {"Content-Type": "application/x-www-form-urlencoded"}
    #         response = requests.post(
    #             introspect_url, data=data, headers=headers, timeout=5
    #         )
    #         response.raise_for_status()
    #         result = response.json()
    #         logging.info("TOKEN DETAILS", result)
    #         return result.get("active", False)
    #     except Exception as e:
    #         logging.error(f"[Keycloak Introspection] Failed: {e}")
    #         return False

    # def get_account_id(self, organization_id: int, token: str):
    #     url = f"{SPOG_BASE_URL}/v1/glass/accounts/{organization_id}/info"
    #     headers = {
    #         "accept": "application/json",
    #         "Authorization": f"Bearer {token}",
    #     }
    #     try:
    #         logging.info(f"get_account_id url:{url}")
    #         response = requests.get(url, headers=headers, timeout=30)
    #         if response.status_code == 200:
    #             logging.info(f"account_id API returned {response.status_code}")
    #             data = response.json()
    #             logging.info(f"account_id :{data.get('id')}")
    #             return data.get("id")
    #         elif response.status_code == 404:
    #             logging.info(f"account_id not found {response.status_code}")
    #             return -1
    #         else:
    #             logging.error(f"Unexpected status code:{response.status_code}")
    #             response.raise_for_status()
    #     except Exception as e:
    #         logging.error(f"Failed to fetch account_id: {e}")
    #         raise

    def oauth_user_info(self, provider, response=None):
        if provider != "keycloak" or not response:
            return {}
        access_token = response.get("access_token")
        if not access_token:
            logging.error("access token not found")
            return {}
        try:
            decoded = jwt.decode(
                access_token, options={"verify_signature": False}
            )
            organization = decoded.get("organization")
            logging.info(f"organization_info:{organization}")
            logging.info(f"organization_id:{organization.get('id')}")
            self.pre_process()
            # account_id = self.get_account_id(
            #     organization.get("id"), access_token
            # )
            account_id = None
            logging.info(f"account_id:{account_id}")
            session["access_token"] = access_token
            session["account_id"] = account_id
            return {
                "username": decoded.get("preferred_username"),
                "email": decoded.get("email"),
                "first_name": decoded.get("given_name", ""),
                "last_name": decoded.get("family_name", ""),
                "account_id": account_id,
                "roles": decoded.get("realm_access", {}).get("roles", []),
            }
        except Exception as e:
            logging.error(f"ERROR Failed to decode JWT: {e}")
            return {}

    def auth_user_oauth(self, userinfo):
        if not userinfo:
            return None

        username = userinfo.get("username")
        email = userinfo.get("email")
        first_name = userinfo.get("first_name")
        last_name = userinfo.get("last_name")
        roles_from_token = userinfo.get("roles", [])

        if not username:
            return None

        user = self.find_user(username=username)
        if not user:
            user = self.add_user(
                username=username,
                first_name=first_name,
                last_name=last_name,
                email=email,
                role=self.find_role("Public"),
            )

        ignored = {
            "offline_access",
            "default-roles-superset",
            "uma_authorization",
        }
        effective_roles = [r for r in roles_from_token if r not in ignored]

        if "SupersetAdmin" in effective_roles:
            admin_role = self.find_role("Admin") or self.add_role("Admin")
            user.roles = [admin_role]
        else:
            dashboard_role = self.find_role(CUSTOM_ROLE) or self.add_role(
                CUSTOM_ROLE
            )
            user.roles = [dashboard_role]

        self.get_session.commit()
        return user

    def load_user_jwt(self, _jwt_header, jwt_data):
        username = jwt_data.get("preferred_username")
        user = self.find_user(username=username)
        if user and user.is_active:
            g.user = user
            return user
        return None


CUSTOM_SECURITY_MANAGER = CustomSecurityManager


# -------------------------------
# JINJA CONTEXT FOR RLS
# -------------------------------
JINJA_CONTEXT_ADDONS = {
    "account_id": lambda: session.get("account_id"),
}

# -------------------------------
# CUSTOM THEME CONFIGURATION
# -------------------------------

# Setting it to '/' would take the user to '/superset/welcome/'
# LOGO_TARGET_PATH = "/"

APP_NAME = "Analytics Dashbords"

APP_ICON = (
    "https://alegflgbdr.cloudimg.io/__applications-dev__/"
    "290b8f32-80d5-497c-897e-2ce0d53c1e3a"
)

# Specify tooltip that should appear when hovering over the App Icon/Logo
LOGO_TOOLTIP = "Analytics Dashbord"

# Specify any text that should appear to the right of the logo
# LOGO_RIGHT_TEXT = "My Department Name"

# FAVICONS = [{"href": "src/app/Images/simplify-logo.svg"}]

THEME_OVERRIDES = {
    "colors": {
        "text": {
            "label": "#A56DD7",  # btTextColor from simplify palette
            "help": "#BCBDCD",  # lightColor from simplify palette
        },
        "primary": {
            "base": "#5514B4",  # primaryColor from simplify palette
            "dark1": "#333333",  # darkColor from simplify palette
            "dark2": "#333333",
            "light1": "#F5F1FA",  # bgLightPrimary from simplify palette
            "light2": "#CCB9E9",  # bgLightMediumPrimay from simplify palette
            "light3": "#FFF5FF",  # bgLightIntermediatePrimary
            "light4": "#EFEAF7",  # bgSlightDarkPrimary from simplify palette
            "light5": "#EBE3F6",  # actionBg from simplify palette
        },
        "secondary": {
            "base": "#F200F5",  # secondaryColor from simplify palette
            "dark1": "#D200D6",  # automationIcons from simplify palette
            "dark2": "#7E2EC6",  # purpleColor from simplify palette
            "light1": "#FF9EB7",  # coralColor from simplify palette
            "light2": "#E0F9FC",  # ratePlanCardColorDefault
            "light3": "#B3EFF8",  # ratePlanCardButtons from simplify palette
            "light4": "#f7f7f9",  # ratePlanCardColor from simplify palette
        },
        "success": {
            "base": "#30B281",  # greenColor from simplify palette
            "dark1": "#30B281",
            "dark2": "#30B281",
            "light1": "#30B281",
            "light2": "#30B281",
        },
        "warning": {
            "base": "#F7735D",  # orangeColor from simplify palette
            "dark1": "#F7735D",
            "dark2": "#F7735D",
            "light1": "#FEEEEC",  # orangeColor100 from simplify palette
            "light2": "#FEEEEC",
        },
        "error": {
            "base": "#EB5F64",  # redColor from simplify palette
            "dark1": "#EB5F64",
            "dark2": "#EB5F64",
            "light1": "#EB5F64",
            "light2": "#EB5F64",
        },
        "info": {
            "base": "#0296D4",  # blueColor from simplify palette
            "dark1": "#0296D4",
            "dark2": "#0296D4",
            "light1": "#00C9E7",  # tealColor from simplify palette
            "light2": "#00C9E7",
        },
        "alert": {
            "base": "#FFDF00",  # yellowColor from simplify palette
            "dark1": "#FFDF00",
            "dark2": "#FFDF00",
            "light1": "#FFDF00",
            "light2": "#FFDF00",
        },
        "grayscale": {
            "base": "#BCBDCD",  # lightColor from simplify palette
            "dark1": "#333333",  # darkColor from simplify palette
            "dark2": "#333333",
            "light1": "#F5F1FA",
            "light2": "#EFEAF7",
            "light3": "#BCBDCD",
            "light4": "#f7f7f9",
            "light5": "#FFFFFF",
        },
    },
    "typography": {
        "fontFamily": "'BTCurve', sans-serif",  # fontFamily
        "weights": {"light": 200, "normal": 400, "medium": 500, "bold": 600},
    },
    "gridUnit": 4,
    "borderRadius": 4,
    "transitionTiming": 0.1,
}


# GLOBAL TOKEN VALIDATION ON EVERY PAGE
def check_keycloak_token_validity():
    if current_user.is_authenticated:
        token = session.get("access_token")
        security_manager = current_app.appbuilder.sm
        if not token or not security_manager.is_token_active(token):
            logging.info("Keycloak session invalid — logging out.")
            logout_user()
            session.clear()
            return redirect("/login")
    return None


def FLASK_APP_MUTATOR(app: Flask) -> None:
    """
    This function is called once after the Flask app is created.
    Use it to modify app-level behavior like sessions, middleware, etc.
    """
    app.before_request_funcs.setdefault(None, []).append(
        check_keycloak_token_validity
    )
