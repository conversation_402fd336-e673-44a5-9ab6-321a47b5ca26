class Config:
    ROLE_NAME = "DashboardOwner"
    RLS_ENABLED = True
    RLS_FILTER_TYPE = "Regular"
    RLS_FILTER = "AccountID = '{{ account_id() }}'"
    RLS_DESCRIPTION = "Account id row level security"
    RLS_GROUP_KEY = None

    # use case
    # RLS_FILTER = {
    #     "DashboardOwner": ["studentID = '{{ studentID() }}'"],
    #     "DashboardOwner": ["TEST", "TRF"],
    #     "test": ["TEST"]
    # }

    RLS_TABLES = {
        "DashboardOwner": ["CDR_DATA", "CDR_VOICE", "CDR_SMS", "CDR_OPERATOR"],
        "default": ["CDR_DATA", "CDR_VOICE", "CDR_SMS", "CDR_OPERATOR"],
    }
