# Entry point for the application
import logging
import os

from config.rls_config import Config
from services.client import SupersetClient
from utils.keycloak import get_keycloak_token

logging.basicConfig(level=logging.INFO)


def main():
    try:
        token = get_keycloak_token()
        logging.info("Token fetched")

        client = SupersetClient(os.getenv("SUPERSET_BASE_URL"), token)

        role_name = Config.ROLE_NAME
        client.create_role(role_name)
        role_id = client.get_role_id_by_name(role_name)

        if Config.RLS_ENABLED:
            table_names = Config.RLS_TABLES.get(
                role_name, Config.RLS_TABLES["default"]
            )
            table_ids = client.get_table_ids_by_names(table_names)
            client.add_rls_policy(
                role_id,
                Config.RLS_FILTER_TYPE,
                Config.RLS_FILTER,
                table_ids,
                Config.RLS_DESCRIPTION,
                Config.RLS_GROUP_KEY,
            )

    except Exception as e:
        logging.critical(f"Unhandled error: {e}")


if __name__ == "__main__":
    main()
