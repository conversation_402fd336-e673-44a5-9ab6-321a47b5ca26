# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
debug.log

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# pyenv
.python-version

# pipenv
Pipfile.lock

# mypy
.mypy_cache/
.dmypy.json

# dotenv
.env
.env.*
!env.example

# VSCode
.vscode/

# System files
.DS_Store
Thumbs.db

# Docker volumes (contain runtime data, should not be committed)
docker/volumes/
