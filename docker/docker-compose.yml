# THIS FILE IS FOR DEVELOPER LOCAL SETUP PURPOSES

version: '3.8'

services:
  # Zookeeper - Required for Ka<PERSON>ka and Pinot coordination
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    hostname: zookeeper
    container_name: zookeeper
    ports:
      - "2181:2181"
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000


  # Kafka - Message streaming platform
  kafka:
    image: confluentinc/cp-kafka:7.4.0
    hostname: kafka
    container_name: kafka
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
      - "9101:9101"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: 'zookeeper:2181'
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0
      KAFKA_JMX_PORT: 9101
      KAFKA_JMX_HOSTNAME: localhost
    volumes:
      - kafka-data:/var/lib/kafka/data

  # Pinot Controller - Manages cluster metadata
  pinot-controller:
    image: apachepinot/pinot:1.0.0
    command: "StartController -zkAddress zookeeper:2181"
    container_name: pinot-controller
    restart: unless-stopped
    ports:
      - "9000:9000"
    environment:
      JAVA_OPTS: "-Dplugins.dir=/opt/pinot/plugins -Xms1G -Xmx4G -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -Xloggc:gc-pinot-controller.log"
    depends_on:
      - zookeeper
    volumes:
      - pinot-controller-data:/tmp/data/controller

  # Pinot Broker - Handles queries
  pinot-broker:
    image: apachepinot/pinot:1.0.0
    command: "StartBroker -zkAddress zookeeper:2181"
    restart: unless-stopped
    container_name: pinot-broker
    ports:
      - "8099:8099"
    environment:
      JAVA_OPTS: "-Dplugins.dir=/opt/pinot/plugins -Xms4G -Xmx4G -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -Xloggc:gc-pinot-broker.log"
    depends_on:
      - pinot-controller
    volumes:
      - pinot-broker-data:/tmp/data/broker

  # Pinot Server - Stores and serves data
  pinot-server:
    image: apachepinot/pinot:1.0.0
    command: "StartServer -zkAddress zookeeper:2181"
    restart: unless-stopped
    container_name: pinot-server
    ports:
      - "8098:8098"
      - "8097:8097"
    environment:
      JAVA_OPTS: "-Dplugins.dir=/opt/pinot/plugins -Xms4G -Xmx16G -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -Xloggc:gc-pinot-server.log"
    depends_on:
      - pinot-broker
    volumes:
      - pinot-server-data:/tmp/data/server

  # Pinot Minion - Background task executor
  pinot-minion:
    image: apachepinot/pinot:1.0.0
    command: "StartMinion -zkAddress zookeeper:2181"
    restart: unless-stopped
    container_name: pinot-minion
    ports:
      - "9514:9514"
    environment:
      JAVA_OPTS: "-Dplugins.dir=/opt/pinot/plugins -Xms4G -Xmx4G -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -Xloggc:gc-pinot-minion.log"
    depends_on:
      - pinot-server
    volumes:
      - pinot-minion-data:/tmp/data/minion
      - ./volumes/pinot/csv-input:/tmp/pinot-data/csv-input
      - ./volumes/pinot/segments:/tmp/pinot-data/segments


  # Redis - Required for Superset
  redis:
    image: redis:7.2-alpine
    container_name: superset-redis
    restart: unless-stopped
    volumes:
      - ./volumes/redis:/data

  # PostgreSQL - Superset metadata database
  postgres:
    image: postgres:15
    container_name: superset-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: superset
      POSTGRES_USER: superset
      POSTGRES_PASSWORD: superset
    volumes:
      - ./volumes/postgres:/var/lib/postgresql/data

  # Apache Superset - Dashboard and visualization
  superset:
    image: apache/superset:4.1.2
    container_name: superset
    restart: unless-stopped
    user: root
    depends_on:
      - postgres
      - redis
      - pinot-broker
    ports:
      - "8088:8088"
    environment:
      - SUPERSET_CONFIG_PATH=/app/superset_config.py
      - SUPERSET_SECRET_KEY=your-secret-key-change-in-production
      - FLASK_ENV=development
      - FLASK_DEBUG=1
      # Database configuration
      - DB_USER=superset
      - DB_PASS=superset
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=superset
      # Redis configuration
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=1
      - REDIS_CELERY_DB=0
      # Keycloak OAuth configuration
      - SUPERSET_BASE_URL=http://localhost:8088
      - ISSUER_URL=http://keycloak:8080/realms/superset
      - CLIENT_ID=superset-client
      - CLIENT_SECRET=your-client-secret-here
      - ENV_DOMAIN=http://keycloak:8080
    volumes:
      - ../src/app/config/superset_config.py:/app/superset_config.py
      - ./volumes/superset:/app/superset_home
    command: >
      bash -c "
        pip install --root-user-action=ignore psycopg2-binary authlib pinotdb flask_cors &&
        superset fab create-admin --username admin --firstname Admin --lastname User --email <EMAIL> --password admin &&
        superset db upgrade &&
        superset init &&
        superset run -h 0.0.0.0 -p 8088 --with-threads --reload --debugger
      "
volumes:
  zookeeper-data:
  zookeeper-logs:
  kafka-data:
  pinot-controller-data:
  pinot-broker-data:
  pinot-server-data:
  pinot-minion-data:

networks:
  default:
    name: pinot-network
    driver: bridge